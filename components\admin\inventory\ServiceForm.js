import { useState, useEffect } from 'react';
import Image from 'next/image';
import ImagePicker from '@/components/admin/ImagePicker';
import { getAuthToken } from '@/lib/auth-token-manager';

import styles from '@/styles/admin/ServiceForm.module.css';

export default function ServiceForm({ service, onSave, onCancel }) {
  console.log('🔍 ServiceForm rendered with props:', { service, onSave: !!onSave, onCancel: !!onCancel });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [showImagePicker, setShowImagePicker] = useState(false);
  const [uploadingImage, setUploadingImage] = useState(false);

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    duration: '60', // in minutes - kept for backward compatibility
    price: '', // kept for backward compatibility
    color: '#6a0dad',
    category: '',
    image_url: '',
    gallery_images: [],
    status: 'active',
    featured: false,
    meta_title: '',
    meta_description: '',
    booking_requirements: '',
    availability_notes: ''
  });

  // Pricing tiers state
  const [pricingTiers, setPricingTiers] = useState([
    {
      id: null,
      name: 'Standard',
      description: 'Standard service duration and pricing',
      duration: '60',
      price: '',
      is_default: true,
      sort_order: 1
    }
  ]);

  // Initialize form data when service prop changes
  useEffect(() => {
    try {
      console.log('🔍 ServiceForm useEffect triggered with service:', service);

      if (service) {
        // Validate service object
        if (typeof service !== 'object' || service === null) {
          console.error('❌ Invalid service object:', service);
          setError('Invalid service data received. Please try again.');
          return;
        }

        console.log('✅ Setting form data from service');
        setFormData({
          name: String(service.name || ''),
          description: String(service.description || ''),
          duration: String(service.duration || 60),
          price: String(service.price || ''),
          color: String(service.color || '#6a0dad'),
          category: String(service.category || ''),
          image_url: String(service.image_url || ''),
          gallery_images: Array.isArray(service.gallery_images) ? service.gallery_images.map(img => String(img)) : [],
          status: String(service.status || 'active'),
          // Handle string boolean conversion from API
          featured: service.featured === 'true' || service.featured === true,
          meta_title: String(service.meta_title || ''),
          meta_description: String(service.meta_description || ''),
          booking_requirements: String(service.booking_requirements || ''),
          availability_notes: String(service.availability_notes || '')
        });

        // Load pricing tiers if available
        if (service.pricing_tiers && Array.isArray(service.pricing_tiers) && service.pricing_tiers.length > 0) {
          console.log('✅ Loading pricing tiers from service');
          setPricingTiers(service.pricing_tiers.map(tier => ({
            id: String(tier.id || ''),
            name: String(tier.name || ''),
            description: String(tier.description || ''),
            duration: String(tier.duration || '60'),
            price: String(tier.price || ''),
            // Handle string boolean conversion from API
            is_default: tier.is_default === 'true' || tier.is_default === true,
            sort_order: Number(tier.sort_order) || 0
          })));
        } else {
          console.log('✅ Creating default pricing tier from service data');
          // Create default tier from service data
          setPricingTiers([{
            id: null,
            name: 'Standard',
            description: 'Standard service duration and pricing',
            duration: String(service.duration || 60),
            price: String(service.price || ''),
            is_default: true,
            sort_order: 1
          }]);
        }

        console.log('✅ ServiceForm initialization complete');
      } else {
        console.log('✅ No service provided, using default form state');
      }
    } catch (error) {
      console.error('❌ Error in ServiceForm useEffect:', error);
      setError('Failed to load service data. Please try again.');
    }
  }, [service]);

  const handleInputChange = (field, value) => {
    // Validate image URL to prevent local file path errors
    if (field === 'image_url' && value) {
      // Check if it's a local file path (Windows or Unix style)
      if (value.match(/^[A-Za-z]:\\/) || value.match(/^\/[^\/]/) || value.match(/^\.\.?\//)) {
        setError('Please use a web URL (starting with http:// or https://) or a relative path (starting with /images/)');
        return;
      }
      // Clear any previous URL validation errors when a valid URL is entered
      if (error && error.includes('Please use a web URL')) {
        setError(null);
      }
    }

    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    // Clear any previous errors
    if (error && !error.includes('Please use a web URL')) setError(null);
  };

  // Pricing tiers management functions
  const handlePricingTierChange = (index, field, value) => {
    // Convert numeric fields to proper types
    let processedValue = value;
    if (field === 'duration' || field === 'price') {
      processedValue = value; // Keep as string for input display, convert to number on submit
    }

    setPricingTiers(prev => prev.map((tier, i) =>
      i === index ? { ...tier, [field]: processedValue } : tier
    ));
  };

  const addPricingTier = () => {
    const newTier = {
      id: null,
      name: '',
      description: '',
      duration: '60',
      price: '',
      is_default: false,
      sort_order: pricingTiers.length
    };
    setPricingTiers(prev => [...prev, newTier]);
  };

  const removePricingTier = (index) => {
    if (pricingTiers.length <= 1) {
      setError('At least one pricing tier is required');
      return;
    }
    setPricingTiers(prev => prev.filter((_, i) => i !== index));
  };

  const setDefaultTier = (index) => {
    setPricingTiers(prev => prev.map((tier, i) => ({
      ...tier,
      is_default: i === index
    })));
  };

  const validateForm = () => {
    if (!formData.name.trim()) {
      setError('Service name is required');
      return false;
    }

    // Validate pricing tiers
    if (pricingTiers.length === 0) {
      setError('At least one pricing tier is required');
      return false;
    }

    const hasDefault = pricingTiers.some(tier => tier.is_default);
    if (!hasDefault) {
      setError('One pricing tier must be marked as default');
      return false;
    }

    for (let i = 0; i < pricingTiers.length; i++) {
      const tier = pricingTiers[i];
      if (!tier.name.trim()) {
        setError(`Pricing tier ${i + 1}: Name is required`);
        return false;
      }
      const price = Number(tier.price);
      const duration = Number(tier.duration);

      if (!tier.price || isNaN(price) || price <= 0) {
        setError(`Pricing tier ${i + 1}: Valid price is required`);
        return false;
      }
      if (!tier.duration || isNaN(duration) || duration <= 0) {
        setError(`Pricing tier ${i + 1}: Valid duration is required`);
        return false;
      }
    }

    return true;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Get default tier for backward compatibility
      const defaultTier = pricingTiers.find(tier => tier.is_default) || pricingTiers[0];

      // Prepare data for API
      const serviceData = {
        ...formData,
        price: Number(defaultTier.price),
        duration: Number(defaultTier.duration),
        pricingTiers: pricingTiers.map((tier, index) => ({
          id: tier.id,
          name: String(tier.name).trim(),
          description: String(tier.description || '').trim(),
          duration: Number(tier.duration),
          price: Number(tier.price),
          is_default: Boolean(tier.is_default),
          sort_order: index
        }))
      };

      // Create or update service
      const url = service ? `/api/admin/services/${service.id}` : '/api/admin/services';
      const method = service ? 'PUT' : 'POST';

      // Get the current auth token
      const token = await getAuthToken();

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token || ''}`
        },
        body: JSON.stringify(serviceData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save service');
      }

      setSuccess('Service saved successfully!');
      setTimeout(() => {
        onSave && onSave();
      }, 1000);

    } catch (error) {
      console.error('Error saving service:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleImageUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    const fileType = file.type.split('/')[1];
    const validTypes = ['jpeg', 'jpg', 'png', 'webp'];

    if (!validTypes.includes(fileType)) {
      setError('Please upload a valid image file (JPEG, PNG, WebP)');
      return;
    }

    setUploadingImage(true);
    setError(null);

    try {
      const formData = new FormData();
      formData.append('image', file);

      // Get the current auth token
      const token = await getAuthToken();

      const response = await fetch('/api/admin/uploads/image', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token || ''}`,
        },
        body: formData
      });

      if (!response.ok) {
        throw new Error('Failed to upload image');
      }

      const data = await response.json();

      handleInputChange('image_url', data.url);
      setSuccess('Image uploaded successfully!');
      setTimeout(() => setSuccess(null), 3000);
    } catch (err) {
      console.error('Error uploading image:', err);
      setError('Failed to upload image. Please try again.');
    } finally {
      setUploadingImage(false);
    }
  };

  return (
    <div className={styles.serviceForm}>
      <div className={styles.formHeader}>
        <h2>{service ? 'Edit Service' : 'Add New Service'}</h2>
      </div>

      {error && (
        <div className={styles.error}>
          {error}
        </div>
      )}

      {success && (
        <div className={styles.success}>
          {success}
        </div>
      )}

      <form onSubmit={handleSubmit} className={styles.form}>
        {/* Basic Information */}
        <div className={styles.section}>
          <h3>Basic Information</h3>

          <div className={styles.formRow}>
            <div className={styles.formGroup}>
              <label htmlFor="name">Service Name *</label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                className={styles.input}
                required
              />
            </div>
            <div className={styles.formGroup}>
              <label htmlFor="category">Category</label>
              <select
                id="category"
                name="category"
                value={formData.category}
                onChange={(e) => handleInputChange('category', e.target.value)}
                className={styles.select}
              >
                <option value="">Select Category</option>
                <option value="painting">Face & Body Painting</option>
                <option value="airbrush">Airbrush Services</option>
                <option value="braiding">Hair Braiding</option>
                <option value="glitter">Glitter Services</option>
                <option value="special">Special Events</option>
              </select>
            </div>
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="description">Description</label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              className={styles.textarea}
              rows={4}
              placeholder="Describe the service, what's included, and any special features..."
            />
          </div>
        </div>

        {/* Pricing Tiers */}
        <div className={styles.section}>
          <div className={styles.sectionHeader}>
            <h3>Pricing Tiers</h3>
            <button
              type="button"
              onClick={addPricingTier}
              className={styles.addTierButton}
            >
              + Add Tier
            </button>
          </div>

          <div className={styles.pricingTiersContainer}>
            {pricingTiers.map((tier, index) => (
              <div key={index} className={styles.pricingTier}>
                <div className={styles.tierHeader}>
                  <h4>Tier {index + 1}</h4>
                  <div className={styles.tierActions}>
                    <label className={styles.defaultLabel}>
                      <input
                        type="radio"
                        name="defaultTier"
                        checked={tier.is_default}
                        onChange={() => setDefaultTier(index)}
                      />
                      Default
                    </label>
                    {pricingTiers.length > 1 && (
                      <button
                        type="button"
                        onClick={() => removePricingTier(index)}
                        className={styles.removeTierButton}
                      >
                        Remove
                      </button>
                    )}
                  </div>
                </div>

                <div className={styles.tierFields}>
                  <div className={styles.formRow}>
                    <div className={styles.formGroup}>
                      <label>Tier Name *</label>
                      <input
                        type="text"
                        name={`tier_name_${index}`}
                        value={String(tier.name || '')}
                        onChange={(e) => handlePricingTierChange(index, 'name', e.target.value)}
                        className={styles.input}
                        placeholder="e.g., Basic, Standard, Premium"
                        required
                      />
                    </div>
                    <div className={styles.formGroup}>
                      <label>Duration (minutes) *</label>
                      <input
                        type="number"
                        name={`tier_duration_${index}`}
                        value={String(tier.duration || '')}
                        onChange={(e) => handlePricingTierChange(index, 'duration', e.target.value)}
                        className={styles.input}
                        min="1"
                        required
                      />
                    </div>
                    <div className={styles.formGroup}>
                      <label>Price (AUD) *</label>
                      <input
                        type="number"
                        name={`tier_price_${index}`}
                        value={String(tier.price || '')}
                        onChange={(e) => handlePricingTierChange(index, 'price', e.target.value)}
                        className={styles.input}
                        step="0.01"
                        min="0"
                        required
                      />
                    </div>
                  </div>
                  <div className={styles.formGroup}>
                    <label>Description</label>
                    <textarea
                      name={`tier_description_${index}`}
                      value={String(tier.description || '')}
                      onChange={(e) => handlePricingTierChange(index, 'description', e.target.value)}
                      className={styles.textarea}
                      rows={2}
                      placeholder="Describe what's included in this tier..."
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className={styles.formRow}>
            <div className={styles.formGroup}>
              <label htmlFor="color">Calendar Color</label>
              <input
                type="color"
                id="color"
                name="color"
                value={formData.color}
                onChange={(e) => handleInputChange('color', e.target.value)}
                className={styles.colorInput}
              />
            </div>
          </div>
        </div>

        {/* Images */}
        <div className={styles.section}>
          <h3>Images</h3>

          <div className={styles.formGroup}>
            <label htmlFor="image_url">Main Image</label>

            {formData.image_url ? (
              <div className={styles.imagePreviewContainer}>
                <div className={styles.imagePreview}>
                  <Image
                    src={formData.image_url}
                    alt="Service image"
                    width={200}
                    height={150}
                    style={{ objectFit: 'cover' }}
                    className={styles.previewImage}
                  />
                  <div className={styles.imageActions}>
                    <input
                      type="file"
                      id="service-image-replace"
                      accept="image/jpeg,image/png,image/webp"
                      onChange={handleImageUpload}
                      disabled={uploadingImage}
                      className={styles.fileInput}
                      style={{ display: 'none' }}
                    />
                    <label htmlFor="service-image-replace" className={styles.changeImageButton}>
                      {uploadingImage ? 'Uploading...' : '📁 Upload New'}
                    </label>
                    <button
                      type="button"
                      onClick={() => setShowImagePicker(true)}
                      className={styles.changeImageButton}
                      disabled={uploadingImage}
                    >
                      🖼️ Select Existing
                    </button>
                    <button
                      type="button"
                      onClick={() => handleInputChange('image_url', '')}
                      className={styles.removeImageButton}
                      disabled={uploadingImage}
                    >
                      Remove
                    </button>
                  </div>
                </div>
                <div className={styles.imagePath}>
                  <strong>Path:</strong> {formData.image_url}
                </div>
              </div>
            ) : (
              <div className={styles.noImageContainer}>
                <div className={styles.noImagePlaceholder}>
                  <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                    <circle cx="8.5" cy="8.5" r="1.5"/>
                    <polyline points="21,15 16,10 5,21"/>
                  </svg>
                  <p>No image selected</p>
                </div>
                <div className={styles.imageUploadOptions}>
                  <div className={styles.uploadOption}>
                    <input
                      type="file"
                      id="service-image-upload"
                      accept="image/jpeg,image/png,image/webp"
                      onChange={handleImageUpload}
                      disabled={uploadingImage}
                      className={styles.fileInput}
                      style={{ display: 'none' }}
                    />
                    <label htmlFor="service-image-upload" className={styles.uploadButton}>
                      {uploadingImage ? 'Uploading...' : '📁 Upload New Image'}
                    </label>
                  </div>
                  <div className={styles.uploadOption}>
                    <button
                      type="button"
                      onClick={() => setShowImagePicker(true)}
                      className={styles.selectImageButton}
                      disabled={uploadingImage}
                    >
                      🖼️ Select Existing Image
                    </button>
                  </div>
                </div>
              </div>
            )}

            <div className={styles.manualInputOption}>
              <details open>
                <summary>Or enter image URL manually</summary>
                <input
                  type="url"
                  id="image_url"
                  name="image_url"
                  value={formData.image_url}
                  onChange={(e) => handleInputChange('image_url', e.target.value)}
                  className={styles.input}
                  placeholder="https://example.com/image.jpg or /images/services/image.jpg"
                  tabIndex="0"
                />
              </details>
            </div>
          </div>
        </div>

        {/* Settings */}
        <div className={styles.section}>
          <h3>Settings</h3>

          <div className={styles.formRow}>
            <div className={styles.formGroup}>
              <label htmlFor="status">Status</label>
              <select
                id="status"
                name="status"
                value={formData.status}
                onChange={(e) => handleInputChange('status', e.target.value)}
                className={styles.select}
              >
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>
            <div className={styles.formGroup}>
              <label className={styles.checkboxLabel}>
                <input
                  type="checkbox"
                  name="featured"
                  checked={formData.featured}
                  onChange={(e) => handleInputChange('featured', e.target.checked)}
                />
                Featured Service
              </label>
            </div>
          </div>
        </div>

        {/* Form Actions */}
        <div className={styles.formActions}>
          <button
            type="button"
            onClick={onCancel}
            className={styles.cancelButton}
            disabled={loading}
          >
            Cancel
          </button>
          <button
            type="submit"
            className={styles.saveButton}
            disabled={loading}
          >
            {loading ? 'Saving...' : (service ? 'Update Service' : 'Create Service')}
          </button>
        </div>
      </form>

      {/* Image Picker Modal */}
      {showImagePicker && (
        <ImagePicker
          onSelect={(imagePath) => {
            handleInputChange('image_url', imagePath);
            setShowImagePicker(false);
          }}
          onCancel={() => setShowImagePicker(false)}
          currentImage={formData.image_url}
          directory="services"
          title="Select Service Image"
        />
      )}
    </div>
  );
}
